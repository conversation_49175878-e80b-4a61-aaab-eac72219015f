import { useState, useEffect } from 'react'
import { useTheme } from '../../context/ThemeContext'
import { useNavigate } from 'react-router-dom'
import {
  ArrowLeft,
  Users,
  Smartphone,
  Globe,
  Calendar,
  DollarSign,
  Wifi,
  Search,
  Filter,
  Check,
  X,
  AlertCircle,
  CheckCircle,
  Clock,
  Download,
  Mail,
  QrCode,
  RefreshCw
} from 'lucide-react'
import toast from 'react-hot-toast'
import { clientService } from '../../services/clientService'
import { traveRoamService } from '../../services/traveRoamService'
import { esimService } from '../../services/esimService'
import { integrationService } from '../../services/integrationService'
import { realtimeService } from '../../services/realtimeService'
import RealtimeEsimStatus from '../../components/esim/RealtimeEsimStatus'

// Sample client data
const sampleClients = [
  {
    id: 1,
    fullName: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-123-4567',
    countryOfTravel: 'US',
    dateOfTravel: '2024-02-15',
    status: 'active'
  },
  {
    id: 2,
    fullName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+44 20 7123 4567',
    countryOfTravel: 'UK',
    dateOfTravel: '2024-02-20',
    status: 'active'
  },
  {
    id: 3,
    fullName: 'Michael Chen',
    email: '<EMAIL>',
    phone: '******-987-6543',
    countryOfTravel: 'JP',
    dateOfTravel: '2024-02-25',
    status: 'active'
  },
  {
    id: 4,
    fullName: 'Emma Rodriguez',
    email: '<EMAIL>',
    phone: '+**************',
    countryOfTravel: 'ES',
    dateOfTravel: '2024-03-01',
    status: 'active'
  }
]

// Sample eSIM plans from TraveRoam API
const sampleEsimPlans = [
  {
    id: 'plan_us_7d_5gb',
    country: 'US',
    countryName: 'United States',
    region: 'North America',
    dataVolume: '5GB',
    validity: 7,
    validityUnit: 'days',
    price: 25.00,
    currency: 'USD',
    network: '4G/5G',
    coverage: 'Nationwide',
    description: 'Perfect for short business trips',
    features: ['Unlimited SMS', 'Voice calls included', 'Hotspot enabled']
  },
  {
    id: 'plan_us_30d_20gb',
    country: 'US',
    countryName: 'United States',
    region: 'North America',
    dataVolume: '20GB',
    validity: 30,
    validityUnit: 'days',
    price: 75.00,
    currency: 'USD',
    network: '4G/5G',
    coverage: 'Nationwide',
    description: 'Ideal for extended stays',
    features: ['Unlimited SMS', 'Voice calls included', 'Hotspot enabled', 'Multi-carrier support']
  },
  {
    id: 'plan_europe_14d_10gb',
    country: 'EU',
    countryName: 'Europe (Multi-country)',
    region: 'Europe',
    dataVolume: '10GB',
    validity: 14,
    validityUnit: 'days',
    price: 45.00,
    currency: 'USD',
    network: '4G/5G',
    coverage: '28 EU countries',
    description: 'Travel across Europe seamlessly',
    features: ['Roaming in 28 countries', 'Unlimited SMS', 'Voice calls included']
  },
  {
    id: 'plan_asia_7d_3gb',
    country: 'ASIA',
    countryName: 'Asia Pacific',
    region: 'Asia Pacific',
    dataVolume: '3GB',
    validity: 7,
    validityUnit: 'days',
    price: 20.00,
    currency: 'USD',
    network: '4G/5G',
    coverage: '15 countries',
    description: 'Explore Asia with reliable connectivity',
    features: ['Multi-country roaming', 'High-speed data', 'Local numbers available']
  },
  {
    id: 'plan_global_30d_50gb',
    country: 'GLOBAL',
    countryName: 'Global Coverage',
    region: 'Worldwide',
    dataVolume: '50GB',
    validity: 30,
    validityUnit: 'days',
    price: 150.00,
    currency: 'USD',
    network: '4G/5G',
    coverage: '150+ countries',
    description: 'Ultimate global connectivity solution',
    features: ['Worldwide coverage', 'Premium support', 'Unlimited SMS', 'Voice calls included']
  }
]

function AssignEsimPage() {
  const { resolvedTheme } = useTheme()
  const navigate = useNavigate()
  const [currentStep, setCurrentStep] = useState(1) // 1: Select Client, 2: Select Plan, 3: Confirm & Assign
  const [selectedClient, setSelectedClient] = useState(null)
  const [selectedPlan, setSelectedPlan] = useState(null)
  const [clients, setClients] = useState([])
  const [esimPlans, setEsimPlans] = useState([])
  const [searchTerm, setSearchTerm] = useState('')
  const [planFilter, setPlanFilter] = useState('all')
  const [isAssigning, setIsAssigning] = useState(false)
  const [assignmentResult, setAssignmentResult] = useState(null)
  const [isLoadingPlans, setIsLoadingPlans] = useState(false)
  const [isLoadingClients, setIsLoadingClients] = useState(true)
  const [error, setError] = useState(null)

  // Filter clients based on search
  const filteredClients = clients.filter(client =>
    client.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Filter plans based on selected filters
  const filteredPlans = esimPlans.filter(plan => {
    if (planFilter === 'all') return true
    if (planFilter === 'regional') return plan.region !== 'Worldwide'
    if (planFilter === 'global') return plan.region === 'Worldwide'
    return true
  })

  // Load clients from API
  const loadClients = async () => {
    setIsLoadingClients(true)
    setError(null)
    try {
      console.log('🔄 Loading clients from API...')
      const response = await clientService.getMyClients({ limit: 100 })

      if (response.success && response.data && response.data.results) {
        const clientsData = response.data.results.map(client => ({
          id: client.id,
          fullName: client.fullName || client.full_name || 'Unknown',
          email: client.email,
          phone: client.phone || client.phone_number,
          countryOfTravel: client.countryOfTravel || client.country_of_travel || 'Unknown',
          dateOfTravel: client.dateOfTravel || client.date_of_travel,
          status: client.status || 'active',
          passportNumber: client.passportNumber || client.passport_number
        }))

        setClients(clientsData)
        console.log('✅ Loaded clients:', clientsData.length)
      } else {
        throw new Error(response.message || 'Failed to load clients')
      }
    } catch (error) {
      console.error('❌ Failed to load clients:', error)
      setError('Failed to load clients: ' + error.message)
      toast.error('Failed to load clients')
    } finally {
      setIsLoadingClients(false)
    }
  }

  // Load eSIM plans from TraveRoam API
  const loadEsimPlans = async (countryFilter = null) => {
    setIsLoadingPlans(true)
    try {
      console.log('🔄 Loading eSIM plans from TraveRoam API...')

      // Get available plans from TraveRoam
      const response = await traveRoamService.getAvailablePlans({
        country: countryFilter,
        region: null
      })

      if (response.success && response.data) {
        const plansData = response.data.map(plan => ({
          id: plan.id || plan.name,
          countryName: plan.country_name || plan.countryName || plan.name,
          country: plan.country_code || plan.country,
          region: plan.region || 'Regional',
          dataVolume: plan.data_volume || plan.dataVolume || 'Unknown',
          validity: plan.validity_days || plan.validity || 30,
          validityUnit: 'days',
          price: plan.price || plan.cost || 0,
          currency: plan.currency || 'USD',
          network: plan.network || 'Multiple',
          coverage: plan.coverage || 'Standard'
        }))

        setEsimPlans(plansData)
        console.log('✅ Loaded eSIM plans:', plansData.length)
      } else {
        throw new Error(response.message || 'Failed to load eSIM plans')
      }
    } catch (error) {
      console.error('❌ Failed to load eSIM plans:', error)
      toast.error('Failed to load eSIM plans: ' + error.message)
      // Fallback to sample data if API fails
      setEsimPlans(sampleEsimPlans)
    } finally {
      setIsLoadingPlans(false)
    }
  }

  // Load clients when component mounts
  useEffect(() => {
    loadClients()
  }, [])

  // Load plans when client is selected
  useEffect(() => {
    if (selectedClient && currentStep === 2) {
      loadEsimPlans(selectedClient.countryOfTravel)
    }
  }, [selectedClient, currentStep])

  // Handle client selection
  const handleClientSelect = (client) => {
    setSelectedClient(client)
    setCurrentStep(2)
  }

  // Handle plan selection
  const handlePlanSelect = (plan) => {
    setSelectedPlan(plan)
    setCurrentStep(3)
  }

  // Handle eSIM assignment
  const handleAssignEsim = async () => {
    if (!selectedClient || !selectedPlan) return

    setIsAssigning(true)

    try {
      console.log('🔄 Starting eSIM assignment process...')

      // Step 1: Validate client data
      const clientValidation = await traveRoamService.validateClient({
        phone_number: selectedClient.phone,
        email: selectedClient.email,
        full_name: selectedClient.fullName,
        passport_id: selectedClient.passportNumber
      })

      if (!clientValidation.success) {
        throw new Error(clientValidation.message || 'Client validation failed')
      }

      // Step 2: Process payment (if needed)
      console.log('💳 Processing payment...')
      const paymentData = {
        amount: selectedPlan.price,
        currency: selectedPlan.currency,
        client_id: selectedClient.id,
        plan_id: selectedPlan.id
      }

      // Step 3: Provision eSIM via TraveRoam
      console.log('🚀 Provisioning eSIM via TraveRoam...')
      const provisionResponse = await traveRoamService.provisionEsim({
        client_data: {
          phone_number: selectedClient.phone,
          email: selectedClient.email,
          full_name: selectedClient.fullName,
          passport_id: selectedClient.passportNumber,
          country_of_travel: selectedClient.countryOfTravel
        },
        bundle_data: {
          name: selectedPlan.id,
          country: selectedPlan.country,
          price: selectedPlan.price,
          data_volume: selectedPlan.dataVolume,
          validity_days: selectedPlan.validity
        },
        payment_data: paymentData
      })

      if (!provisionResponse.success) {
        throw new Error(provisionResponse.message || 'eSIM provisioning failed')
      }

      // Step 4: Save to database
      console.log('💾 Saving eSIM data to database...')
      const saveResponse = await esimService.createEsim({
        client_id: selectedClient.id,
        plan_id: selectedPlan.id,
        iccid: provisionResponse.data.iccid,
        qr_code: provisionResponse.data.qr_code,
        activation_code: provisionResponse.data.activation_code,
        status: 'provisioned',
        expiry_date: provisionResponse.data.expiry_date
      })

      // Step 5: Send email to client
      console.log('📧 Sending eSIM details to client...')
      await integrationService.sendEsimEmail({
        client_email: selectedClient.email,
        client_name: selectedClient.fullName,
        esim_data: provisionResponse.data,
        plan_data: selectedPlan
      })

      const result = {
        success: true,
        esimId: provisionResponse.data.esim_id || saveResponse.data?.id,
        qrCode: provisionResponse.data.qr_code,
        activationCode: provisionResponse.data.activation_code,
        status: 'provisioned',
        expiryDate: provisionResponse.data.expiry_date,
        iccid: provisionResponse.data.iccid
      }

      setAssignmentResult(result)
      toast.success('eSIM assigned successfully!')
      console.log('✅ eSIM assignment completed successfully')

    } catch (error) {
      console.error('❌ Failed to assign eSIM:', error)
      toast.error('Failed to assign eSIM: ' + error.message)
    } finally {
      setIsAssigning(false)
    }
  }

  // Handle going back
  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
      if (currentStep === 2) {
        setSelectedClient(null)
      } else if (currentStep === 3) {
        setSelectedPlan(null)
      }
    } else {
      navigate(-1)
    }
  }

  // Handle starting over
  const handleStartOver = () => {
    setCurrentStep(1)
    setSelectedClient(null)
    setSelectedPlan(null)
    setAssignmentResult(null)
    setSearchTerm('')
    setPlanFilter('all')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleBack}
            className="flex items-center space-x-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back</span>
          </button>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Assign eSIM Plan</h1>
            <p className="text-muted-foreground">Select a client and assign an eSIM plan via TraveRoam</p>
          </div>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="bg-card border border-border rounded-lg p-6">
        <div className="flex items-center justify-between">
          {[
            { step: 1, title: 'Select Client', icon: Users },
            { step: 2, title: 'Choose Plan', icon: Smartphone },
            { step: 3, title: 'Confirm & Assign', icon: CheckCircle }
          ].map((item, index) => {
            const Icon = item.icon
            const isActive = currentStep === item.step
            const isCompleted = currentStep > item.step
            const isLast = index === 2

            return (
              <div key={item.step} className="flex items-center">
                <div className={`flex items-center space-x-3 ${
                  isActive ? 'text-primary' : 
                  isCompleted ? 'text-green-500' : 
                  'text-muted-foreground'
                }`}>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
                    isActive ? 'border-primary bg-primary/10' :
                    isCompleted ? 'border-green-500 bg-green-500/10' :
                    'border-muted-foreground/30 bg-muted/30'
                  }`}>
                    {isCompleted ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <Icon className="h-4 w-4" />
                    )}
                  </div>
                  <span className="text-sm font-medium">{item.title}</span>
                </div>
                {!isLast && (
                  <div className={`w-16 h-0.5 mx-4 ${
                    isCompleted ? 'bg-green-500' : 'bg-muted-foreground/30'
                  }`} />
                )}
              </div>
            )
          })}
        </div>
      </div>

      {/* Step 1: Select Client */}
      {currentStep === 1 && (
        <div className="bg-card border border-border rounded-lg p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-foreground">Select Client</h2>
              <div className="flex items-center space-x-4">
                <button
                  onClick={loadClients}
                  disabled={isLoadingClients}
                  className="flex items-center space-x-2 px-3 py-2 bg-muted text-muted-foreground rounded-lg hover:bg-muted/80 transition-colors disabled:opacity-50"
                >
                  <RefreshCw className={`h-4 w-4 ${isLoadingClients ? 'animate-spin' : ''}`} />
                  <span>Refresh</span>
                </button>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <input
                    type="text"
                    placeholder="Search clients..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    disabled={isLoadingClients}
                  />
                </div>
              </div>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <AlertCircle className="h-5 w-5 text-red-500" />
                  <p className="text-red-700">{error}</p>
                </div>
              </div>
            )}

            {isLoadingClients ? (
              <div className="text-center py-8">
                <div className="w-8 h-8 border-2 border-primary/30 border-t-primary rounded-full animate-spin mx-auto mb-4" />
                <p className="text-muted-foreground">Loading clients...</p>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredClients.map((client) => (
                    <button
                      key={client.id}
                      onClick={() => handleClientSelect(client)}
                      className="p-4 border border-border rounded-lg text-left hover:bg-muted/50 transition-colors"
                    >
                      <div className="space-y-2">
                        <h3 className="font-medium text-foreground">{client.fullName}</h3>
                        <p className="text-sm text-muted-foreground">{client.email}</p>
                        <p className="text-sm text-muted-foreground">{client.phone}</p>
                        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                          <Globe className="h-3 w-3" />
                          <span>Traveling to: {client.countryOfTravel}</span>
                        </div>
                        {client.dateOfTravel && (
                          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                            <Calendar className="h-3 w-3" />
                            <span>Travel date: {new Date(client.dateOfTravel).toLocaleDateString()}</span>
                          </div>
                        )}
                      </div>
                    </button>
                  ))}
                </div>

                {filteredClients.length === 0 && !isLoadingClients && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Users className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>No clients found</p>
                    <p className="text-sm">Try adjusting your search or add a new client first</p>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      )}

      {/* Step 2: Choose Plan */}
      {currentStep === 2 && selectedClient && (
        <div className="space-y-6">
          {/* Selected Client Info */}
          <div className="bg-card border border-border rounded-lg p-4">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <Users className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h3 className="font-medium text-foreground">Selected Client: {selectedClient.fullName}</h3>
                <p className="text-sm text-muted-foreground">
                  {selectedClient.email} • Traveling to {selectedClient.countryOfTravel}
                </p>
              </div>
            </div>
          </div>

          {/* Plan Selection */}
          <div className="bg-card border border-border rounded-lg p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-foreground">Available eSIM Plans</h2>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Filter className="h-4 w-4 text-muted-foreground" />
                    <select
                      value={planFilter}
                      onChange={(e) => setPlanFilter(e.target.value)}
                      className="border border-border rounded-lg bg-background text-foreground px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    >
                      <option value="all">All Plans</option>
                      <option value="regional">Regional Plans</option>
                      <option value="global">Global Plans</option>
                    </select>
                  </div>
                  <button
                    onClick={() => loadEsimPlans(selectedClient.countryOfTravel)}
                    disabled={isLoadingPlans}
                    className="flex items-center space-x-2 px-3 py-2 bg-muted text-muted-foreground rounded-lg hover:bg-muted/80 transition-colors disabled:opacity-50"
                  >
                    <RefreshCw className={`h-4 w-4 ${isLoadingPlans ? 'animate-spin' : ''}`} />
                    <span>Refresh Plans</span>
                  </button>
                </div>
              </div>

              {isLoadingPlans ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="p-4 border border-border rounded-lg animate-pulse">
                      <div className="space-y-3">
                        <div className="w-24 h-4 bg-muted rounded"></div>
                        <div className="w-32 h-6 bg-muted rounded"></div>
                        <div className="w-full h-4 bg-muted rounded"></div>
                        <div className="w-20 h-8 bg-muted rounded"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredPlans.map((plan) => (
                    <button
                      key={plan.id}
                      onClick={() => handlePlanSelect(plan)}
                      className="p-4 border border-border rounded-lg text-left hover:bg-muted/50 hover:border-primary/50 transition-all"
                    >
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium text-foreground">{plan.countryName}</h3>
                          <div className="text-right">
                            <p className="text-lg font-bold text-primary">${plan.price}</p>
                            <p className="text-xs text-muted-foreground">{plan.currency}</p>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2 text-sm">
                            <Wifi className="h-3 w-3 text-muted-foreground" />
                            <span className="text-foreground">{plan.dataVolume}</span>
                            <span className="text-muted-foreground">•</span>
                            <span className="text-foreground">{plan.validity} {plan.validityUnit}</span>
                          </div>
                          
                          <div className="flex items-center space-x-2 text-sm">
                            <Globe className="h-3 w-3 text-muted-foreground" />
                            <span className="text-muted-foreground">{plan.coverage}</span>
                          </div>
                        </div>

                        <p className="text-xs text-muted-foreground">{plan.description}</p>

                        <div className="flex flex-wrap gap-1">
                          {plan.features.slice(0, 2).map((feature, index) => (
                            <span key={index} className="px-2 py-1 bg-muted/50 text-xs text-muted-foreground rounded">
                              {feature}
                            </span>
                          ))}
                          {plan.features.length > 2 && (
                            <span className="px-2 py-1 bg-muted/50 text-xs text-muted-foreground rounded">
                              +{plan.features.length - 2} more
                            </span>
                          )}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}

              {!isLoadingPlans && filteredPlans.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Smartphone className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>No eSIM plans available</p>
                  <p className="text-sm">Please try refreshing or contact support</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Step 3: Confirm & Assign */}
      {currentStep === 3 && selectedClient && selectedPlan && !assignmentResult && (
        <div className="space-y-6">
          {/* Assignment Summary */}
          <div className="bg-card border border-border rounded-lg p-6">
            <h2 className="text-lg font-semibold text-foreground mb-4">Assignment Summary</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Client Details */}
              <div className="space-y-3">
                <h3 className="font-medium text-foreground flex items-center space-x-2">
                  <Users className="h-4 w-4" />
                  <span>Client Details</span>
                </h3>
                <div className="bg-muted/30 p-3 rounded-lg space-y-2">
                  <p className="text-sm"><span className="font-medium">Name:</span> {selectedClient.fullName}</p>
                  <p className="text-sm"><span className="font-medium">Email:</span> {selectedClient.email}</p>
                  <p className="text-sm"><span className="font-medium">Phone:</span> {selectedClient.phone}</p>
                  <p className="text-sm"><span className="font-medium">Travel to:</span> {selectedClient.countryOfTravel}</p>
                </div>
              </div>

              {/* Plan Details */}
              <div className="space-y-3">
                <h3 className="font-medium text-foreground flex items-center space-x-2">
                  <Smartphone className="h-4 w-4" />
                  <span>eSIM Plan Details</span>
                </h3>
                <div className="bg-muted/30 p-3 rounded-lg space-y-2">
                  <p className="text-sm"><span className="font-medium">Plan:</span> {selectedPlan.countryName}</p>
                  <p className="text-sm"><span className="font-medium">Data:</span> {selectedPlan.dataVolume}</p>
                  <p className="text-sm"><span className="font-medium">Validity:</span> {selectedPlan.validity} {selectedPlan.validityUnit}</p>
                  <p className="text-sm"><span className="font-medium">Price:</span> ${selectedPlan.price} {selectedPlan.currency}</p>
                  <p className="text-sm"><span className="font-medium">Coverage:</span> {selectedPlan.coverage}</p>
                </div>
              </div>
            </div>

            {/* Assignment Actions */}
            <div className="mt-6 pt-6 border-t border-border">
              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">
                  <p>The eSIM will be provisioned via TraveRoam API and delivered to the client's email.</p>
                </div>
                <div className="flex items-center space-x-4">
                  <button
                    onClick={handleStartOver}
                    disabled={isAssigning}
                    className="flex items-center space-x-2 px-4 py-2 border border-border rounded-lg text-muted-foreground hover:text-foreground hover:bg-muted transition-colors disabled:opacity-50"
                  >
                    <X className="h-4 w-4" />
                    <span>Start Over</span>
                  </button>
                  <button
                    onClick={handleAssignEsim}
                    disabled={isAssigning}
                    className="flex items-center space-x-2 px-6 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50"
                  >
                    {isAssigning ? (
                      <>
                        <div className="w-4 h-4 border-2 border-primary-foreground/30 border-t-primary-foreground rounded-full animate-spin" />
                        <span>Assigning eSIM...</span>
                      </>
                    ) : (
                      <>
                        <Smartphone className="h-4 w-4" />
                        <span>Assign eSIM</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Assignment Result */}
      {assignmentResult && (
        <div className="bg-card border border-border rounded-lg p-6">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-green-500/10 rounded-full flex items-center justify-center mx-auto">
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
            
            <div>
              <h2 className="text-xl font-semibold text-foreground">eSIM Assigned Successfully!</h2>
              <p className="text-muted-foreground">
                The eSIM has been provisioned and sent to {selectedClient.email}
              </p>
            </div>

            <div className="bg-muted/30 p-4 rounded-lg max-w-md mx-auto">
              <div className="space-y-2 text-sm">
                <p><span className="font-medium">eSIM ID:</span> {assignmentResult.esimId}</p>
                <p><span className="font-medium">Status:</span> {assignmentResult.status}</p>
                <p><span className="font-medium">Expires:</span> {new Date(assignmentResult.expiryDate).toLocaleDateString()}</p>
              </div>
            </div>

            {/* Real-time eSIM Status */}
            <div className="max-w-2xl mx-auto">
              <RealtimeEsimStatus
                esimId={assignmentResult.esimId}
                autoStart={true}
                onStatusChange={(update) => {
                  console.log('Real-time status update:', update)
                  if (update.completed) {
                    toast.success(`eSIM provisioning completed! Status: ${update.status}`)
                  } else if (update.error) {
                    toast.error(`eSIM monitoring error: ${update.error}`)
                  } else {
                    toast.info(`eSIM status updated: ${update.status}`)
                  }
                }}
              />
            </div>

            <div className="flex items-center justify-center space-x-4">
              <button
                onClick={handleStartOver}
                className="flex items-center space-x-2 px-4 py-2 border border-border rounded-lg text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
              >
                <Smartphone className="h-4 w-4" />
                <span>Assign Another eSIM</span>
              </button>
              <button
                onClick={() => navigate('/reseller-dashboard')}
                className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Dashboard</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AssignEsimPage
